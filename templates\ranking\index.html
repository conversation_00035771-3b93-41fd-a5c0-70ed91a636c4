{% extends "base.html" %}

{% block title %}排行榜 - 学生学业数据管理与分析平台{% endblock %}

{% block extra_head %}
<!-- 禁用缓存 -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-trophy me-2"></i>
            学生排行榜
        </h1>
    </div>
</div>

<div class="row">
    <!-- 排行榜主要内容 -->
    <div class="col-12">
        <!-- 筛选条件 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    筛选条件
                </h5>
            </div>
            <div class="card-body">
                <form id="filterForm" class="row g-3">
                    <div class="col-md-3">
                        <label for="academicYear" class="form-label">学年</label>
                        <select class="form-select" id="academicYear" name="academic_year">
                            <option value="">全部学年</option>
                            {% for semester in filters.semesters %}
                            <option value="{{ semester.academic_year }}">{{ semester.academic_year }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="semester" class="form-label">学期</label>
                        <select class="form-select" id="semester" name="semester">
                            <option value="">全部学期</option>
                            <option value="1">第一学期</option>
                            <option value="2">第二学期</option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="major" class="form-label">专业</label>
                        <select class="form-select" id="major" name="major">
                            <option value="">全部专业</option>
                            {% for major in filters.majors %}
                            <option value="{{ major }}">{{ major }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="grade" class="form-label">年级</label>
                        <select class="form-select" id="grade" name="grade">
                            <option value="">全部年级</option>
                            {% for grade in filters.grades %}
                            <option value="{{ grade }}">{{ grade }}级</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>查询
                        </button>
                        <button type="button" class="btn btn-outline-secondary me-2" id="resetBtn">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="button" class="btn btn-success me-2" id="exportBtn">
                            <i class="fas fa-download me-1"></i>导出数据
                        </button>
                        <button type="button" class="btn btn-outline-info" id="columnConfigBtn">
                            <i class="fas fa-columns me-1"></i>显示列配置
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 排序配置 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sort me-2"></i>
                    排序配置
                </h5>
            </div>
            <div class="card-body">
                <div id="sortConfig">
                    <div class="row g-3 sort-item">
                        <div class="col-md-4">
                            <select class="form-select sort-field">
                                <option value="total_score">总分</option>
                                <option value="academic_score">学业成绩</option>
                                <option value="comprehensive_score">综合素质分</option>
                                <option value="total_rank">总排名</option>
                                <option value="academic_rank">学业排名</option>
                                <option value="grade">年级</option>
                                <option value="moral_score">德育分</option>
                                <option value="social_work_score">社会工作分</option>
                                <option value="research_score">科研分</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select sort-direction">
                                <option value="desc">降序</option>
                                <option value="asc">升序</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-success btn-sm me-2" onclick="addSortField()">
                                <i class="fas fa-plus"></i> 添加排序
                            </button>
                            <button type="button" class="btn btn-primary btn-sm" onclick="applySortConfiguration()">
                                <i class="fas fa-check"></i> 应用排序
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索框 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索学号或姓名...">
                    <button class="btn btn-primary" type="button" id="searchBtn">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                        <i class="fas fa-times me-1"></i>清除
                    </button>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <div id="resultInfo" class="text-muted"></div>
            </div>
        </div>

        <!-- 排行榜表格 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            排行榜数据
                        </h5>
                        {% if not current_user.is_student() %}
                        <button type="button" class="btn btn-outline-primary btn-sm" id="aiAssistantBtn">
                            <i class="fas fa-robot me-1"></i>AI助手
                        </button>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <div id="loadingSpinner" class="text-center" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载数据...</p>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover ranking-table" id="rankingTable">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>学号</th>
                                        <th>姓名</th>
                                        <th>专业</th>
                                        <th>班级</th>
                                        <th>学年学期</th>
                                        <th>总分</th>
                                        <th>学业成绩</th>
                                        <th>综合素质分</th>
                                        <th>奖学金等级</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="rankingTableBody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="排行榜分页" id="paginationContainer" style="display: none;">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 列显示配置模态框 -->
<div class="modal fade" id="columnConfigModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-columns me-2"></i>
                    显示列配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted mb-3">选择要在排行榜中显示的列：</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="student_id"
                                id="col_student_id" checked disabled>
                            <label class="form-check-label" for="col_student_id">学号（必显示）</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="name" id="col_name"
                                checked disabled>
                            <label class="form-check-label" for="col_name">姓名（必显示）</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="major_name"
                                id="col_major_name" checked>
                            <label class="form-check-label" for="col_major_name">专业</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="class_name"
                                id="col_class_name" checked>
                            <label class="form-check-label" for="col_class_name">班级</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="academic_year_semester"
                                id="col_academic_year_semester" checked>
                            <label class="form-check-label" for="col_academic_year_semester">学年学期</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="total_score"
                                id="col_total_score" checked>
                            <label class="form-check-label" for="col_total_score">总分</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="academic_score"
                                id="col_academic_score" checked>
                            <label class="form-check-label" for="col_academic_score">学业成绩</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="comprehensive_score"
                                id="col_comprehensive_score" checked>
                            <label class="form-check-label" for="col_comprehensive_score">综合素质分</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input column-toggle" type="checkbox" value="award_level"
                                id="col_award_level" checked>
                            <label class="form-check-label" for="col_award_level">奖学金等级</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="applyColumnConfig">应用配置</button>
            </div>
        </div>
    </div>
</div>

<!-- AI助手模态框 -->
<div class="modal fade" id="aiAssistantModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-robot me-2"></i>
                    AI辅助SQL查询
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- AI对话区域 -->
                <div class="mb-3">
                    <label for="aiQuery" class="form-label">向AI描述您的查询需求：</label>
                    <textarea class="form-control" id="aiQuery" rows="3"
                        placeholder="例如：查询2023-2024学年第一学期计算机专业总分前10名的学生"></textarea>
                </div>

                <div class="mb-3">
                    <button type="button" class="btn btn-primary btn-sm me-2" id="generateSqlBtn">
                        <i class="fas fa-magic me-1"></i>生成SQL
                    </button>
                    <button type="button" class="btn btn-success btn-sm" id="executeSqlBtn" disabled>
                        <i class="fas fa-play me-1"></i>执行查询
                    </button>
                </div>

                <!-- 生成的SQL显示区域 -->
                <div class="mb-3" id="sqlResultArea" style="display: none;">
                    <label class="form-label">生成的SQL语句：</label>
                    <div class="position-relative">
                        <textarea class="form-control" id="generatedSql" rows="4" readonly></textarea>
                        <button type="button" class="btn btn-outline-secondary btn-sm position-absolute top-0 end-0 m-1"
                            id="copySqlBtn" title="复制SQL">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>

                <!-- AI分析结果 -->
                <div id="aiAnalysisArea" style="display: none;">
                    <label class="form-label">AI分析结果：</label>
                    <div class="ai-analysis" id="aiAnalysisContent">
                        <!-- AI分析内容 -->
                    </div>
                </div>

                <!-- 加载状态 -->
                <div id="aiLoadingArea" class="text-center" style="display: none;">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">AI处理中...</span>
                    </div>
                    <small class="text-muted ms-2">AI正在处理您的请求...</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-outline-danger" onclick="clearAIData()">
                    <i class="fas fa-trash me-1"></i>清空
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 学生详情模态框 -->
<div class="modal fade" id="studentDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user me-2"></i>
                    学生详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="studentDetailContent">
                <!-- 详情内容将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="viewStudentAnalysis()">查看分析</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    $(document).ready(function () {
        let currentPage = 1;
        let currentFilters = {};

        // 初始加载数据
        loadRankingData();
        
        // 初始化列配置
        initColumnConfiguration();

        // 筛选表单提交
        $('#filterForm').on('submit', function (e) {
            e.preventDefault();
            currentPage = 1;
            loadRankingData();
        });

        // 重置按钮
        $('#resetBtn').on('click', function () {
            $('#filterForm')[0].reset();
            
            // 重置排序配置
            resetSortConfiguration();
            
            // 重置列显示配置
            resetColumnConfiguration();
            
            currentPage = 1;
            loadRankingData();
        });

        // 搜索功能
        $('#searchBtn').on('click', function () {
            performSearch();
        });

        $('#clearSearchBtn').on('click', function () {
            $('#searchInput').val('');
            loadRankingData();
        });

        // 支持回车键搜索
        $('#searchInput').on('keypress', function (e) {
            if (e.which === 13) {  // Enter key
                performSearch();
            }
        });

        function performSearch() {
            const keyword = $('#searchInput').val().trim();
            if (keyword.length >= 1) {
                searchStudents(keyword);
            } else {
                loadRankingData();
            }
        }

        // 导出按钮
        $('#exportBtn').on('click', function () {
            exportData();
        });

        // 列配置按钮
        $('#columnConfigBtn').on('click', function () {
            $('#columnConfigModal').modal('show');
        });

        // 应用列配置
        $('#applyColumnConfig').on('click', function () {
            applyColumnConfiguration();
            $('#columnConfigModal').modal('hide');
        });

        // AI助手按钮
        $('#aiAssistantBtn').on('click', function () {
            $('#aiAssistantModal').modal('show');
        });

        // AI功能按钮
        $('#generateSqlBtn').on('click', function () {
            generateAISQL();
        });

        $('#executeSqlBtn').on('click', function () {
            executeGeneratedSQL();
        });

        $('#copySqlBtn').on('click', function () {
            copyToClipboard($('#generatedSql').val());
        });
    });

    function loadRankingData(page = 1) {
        currentPage = page;
        showLoading(true);

        // 获取筛选条件
        const formData = new FormData($('#filterForm')[0]);
        const params = new URLSearchParams();

        for (let [key, value] of formData.entries()) {
            if (value) {
                params.append(key, value);
            }
        }

        params.append('page', page);
        params.append('per_page', 20);

        currentFilters = Object.fromEntries(params);

        $.get('/ranking/api/data?' + params.toString())
            .done(function (response) {
                if (response.success) {
                    displayRankingData(response.data);
                    updatePagination(response.page, response.per_page, response.total);
                    updateResultInfo(response.total, response.page, response.per_page);
                } else {
                    showError('加载数据失败: ' + response.error);
                }
            })
            .fail(function () {
                showError('网络错误，请稍后重试');
            })
            .always(function () {
                showLoading(false);
            });
    }

    function displayRankingData(data) {
        const tbody = $('#rankingTableBody');
        tbody.empty();

        if (data.length === 0) {
            tbody.append(`
            <tr>
                <td colspan="11" class="text-center text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    暂无数据
                </td>
            </tr>
        `);
            return;
        }

        data.forEach((student, index) => {
            const rank = student.total_rank || ((currentPage - 1) * 20 + index + 1);
            const rankClass = getRankClass(rank);

            const row = `
            <tr class="${rankClass}">
                <td>
                    <span class="badge ${getRankBadgeClass(rank)}">${rank}</span>
                </td>
                <td>${student.student_id}</td>
                <td>${student.name}</td>
                <td>${student.major_name}</td>
                <td>${student.class_name}</td>
                <td>${student.academic_year}-${student.semester}</td>
                <td>
                    <span class="badge bg-primary">${student.total_score && !isNaN(student.total_score) ? parseFloat(student.total_score).toFixed(2) : '未知'}</span>
                </td>
                <td>${student.academic_score && !isNaN(student.academic_score) ? parseFloat(student.academic_score).toFixed(2) : '未知'}</td>
                <td>${student.comprehensive_score && !isNaN(student.comprehensive_score) ? parseFloat(student.comprehensive_score).toFixed(2) : '未知'}</td>
                <td>
                    ${student.award_level ?
                    `<span class="badge ${getAwardBadgeClass(student.award_level)}">${student.award_level}</span>` :
                    '<span class="text-muted">无</span>'
                }
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="showStudentDetail('${student.student_id}')">
                        <i class="fas fa-eye"></i> 详情
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="viewStudentAnalysis('${student.student_id}')">
                        <i class="fas fa-chart-line"></i> 分析
                    </button>
                </td>
            </tr>
        `;
            tbody.append(row);
        });
        
        // 重新应用列配置
        const savedColumns = localStorage.getItem('ranking_visible_columns');
        if (savedColumns) {
            const visibleColumns = JSON.parse(savedColumns);
            updateTableColumnVisibility(visibleColumns);
        }
    }

    function getRankClass(rank) {
        if (rank <= 3) return 'rank-top';
        if (rank <= 10) return 'rank-good';
        return '';
    }

    function getRankBadgeClass(rank) {
        if (rank === 1) return 'bg-warning';
        if (rank === 2) return 'bg-secondary';
        if (rank === 3) return 'bg-info';
        if (rank <= 10) return 'bg-success';
        return 'bg-light text-dark';
    }

    function getAwardBadgeClass(award) {
        if (award.includes('一等')) return 'bg-warning';
        if (award.includes('二等')) return 'bg-info';
        if (award.includes('三等')) return 'bg-success';
        return 'bg-secondary';
    }

    function updatePagination(page, perPage, total) {
        const totalPages = Math.ceil(total / perPage);
        const pagination = $('#pagination');
        pagination.empty();

        if (totalPages <= 1) {
            $('#paginationContainer').hide();
            return;
        }

        $('#paginationContainer').show();

        // 上一页
        pagination.append(`
        <li class="page-item ${page === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadRankingData(${page - 1}); return false;">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `);

        // 页码
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(totalPages, page + 2);

        for (let i = startPage; i <= endPage; i++) {
            pagination.append(`
            <li class="page-item ${i === page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadRankingData(${i}); return false;">${i}</a>
            </li>
        `);
        }

        // 下一页
        pagination.append(`
        <li class="page-item ${page === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadRankingData(${page + 1}); return false;">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `);
    }

    function updateResultInfo(total, page, perPage) {
        const start = (page - 1) * perPage + 1;
        const end = Math.min(page * perPage, total);
        $('#resultInfo').text(`显示第 ${start}-${end} 条，共 ${total} 条记录`);
    }

    function searchStudents(keyword) {
        showLoading(true);

        const params = new URLSearchParams();
        params.append('keyword', keyword);

        // 添加当前筛选条件
        const formData = new FormData($('#filterForm')[0]);
        for (let [key, value] of formData.entries()) {
            if (value && key !== 'order_by') {
                params.append(key, value);
            }
        }

        $.get('/ranking/api/search?' + params.toString())
            .done(function (response) {
                if (response.success) {
                    displayRankingData(response.data);
                    $('#paginationContainer').hide();
                    updateResultInfo(response.data.length, 1, response.data.length);
                } else {
                    showError('搜索失败: ' + response.error);
                }
            })
            .fail(function () {
                showError('搜索失败，请稍后重试');
            })
            .always(function () {
                showLoading(false);
            });
    }

    function showStudentDetail(studentId) {
        // 存储学生ID到模态框数据中
        $('#studentDetailModal').data('student-id', studentId);
        
        // 显示加载状态
        $('#studentDetailContent').html(`
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载学生详情...</p>
        </div>
    `);

        $('#studentDetailModal').modal('show');

        // 加载学生基本信息
        $.ajax({
            url: `/analysis/api/student/${studentId}/trends`,
            method: 'GET',
            success: function(response) {
                if (response.success && response.data.length > 0) {
                    const latestRecord = response.data[response.data.length - 1];
                    const detailHtml = `
                        <div class="row g-3">
                            <div class="col-md-6">
                                <h6><i class="fas fa-user me-2"></i>基本信息</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>学号：</strong></td><td>${studentId}</td></tr>
                                    <tr><td><strong>最新学期：</strong></td><td>${latestRecord.semester}</td></tr>
                                    <tr><td><strong>总分：</strong></td><td>${latestRecord.total_score.toFixed(2)}</td></tr>
                                    <tr><td><strong>总排名：</strong></td><td>${latestRecord.total_rank || 'N/A'}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-chart-bar me-2"></i>成绩构成</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>学业成绩：</strong></td><td>${latestRecord.academic_score.toFixed(2)}</td></tr>
                                    <tr><td><strong>学业排名：</strong></td><td>${latestRecord.academic_rank || 'N/A'}</td></tr>
                                    <tr><td><strong>综合素质分：</strong></td><td>${latestRecord.comprehensive_score.toFixed(2)}</td></tr>
                                </table>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6><i class="fas fa-chart-line me-2"></i>学期记录</h6>
                                <p class="text-muted">该学生共有 ${response.data.length} 个学期的成绩记录</p>
                                <div class="d-flex gap-2 flex-wrap">
                                    ${response.data.map(record => `
                                        <span class="badge bg-secondary">${record.semester}</span>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    `;
                    $('#studentDetailContent').html(detailHtml);
                } else {
                    $('#studentDetailContent').html(`
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            未找到该学生的数据记录
                        </div>
                    `);
                }
            },
            error: function() {
                $('#studentDetailContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        加载学生详情失败，请稍后重试
                    </div>
                `);
            }
        });
    }

    function exportData() {
        const params = new URLSearchParams(currentFilters);
        window.open('/ranking/export?' + params.toString(), '_blank');
    }

    function showLoading(show) {
        if (show) {
            $('#loadingSpinner').show();
            $('#rankingTable').hide();
        } else {
            $('#loadingSpinner').hide();
            $('#rankingTable').show();
        }
    }

    function showError(message) {
        $('#rankingTableBody').html(`
        <tr>
            <td colspan="11" class="text-center text-danger">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i><br>
                ${message}
            </td>
        </tr>
    `);
    }

    // AI功能相关函数
    function generateAISQL() {
        const query = $('#aiQuery').val().trim();
        if (!query) {
            alert('请输入查询需求描述');
            return;
        }

        $('#aiLoadingArea').show();
        $('#generateSqlBtn').prop('disabled', true);

        $.ajax({
            url: '/api/sql_assistant',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                description: query
            }),
            success: function (response) {
                if (response.success) {
                    $('#generatedSql').val(response.data.sql);
                    $('#sqlResultArea').show();
                    $('#executeSqlBtn').prop('disabled', false);

                    // 显示AI分析说明
                    $('#aiAnalysisContent').html(`
                        <div class="alert alert-info">
                            <strong>查询描述：</strong>${response.data.description}<br>
                            <strong>生成的SQL：</strong>已显示在上方文本框中
                        </div>
                    `);
                    $('#aiAnalysisArea').show();
                } else {
                    alert('生成SQL失败: ' + response.error);
                }
            },
            error: function () {
                alert('生成SQL失败，请稍后重试');
            },
            complete: function () {
                $('#aiLoadingArea').hide();
                $('#generateSqlBtn').prop('disabled', false);
            }
        });
    }

    function executeGeneratedSQL() {
        const sql = $('#generatedSql').val();
        if (!sql) {
            alert('没有可执行的SQL语句');
            return;
        }

        $('#aiLoadingArea').show();
        $('#executeSqlBtn').prop('disabled', true);

        $.ajax({
            url: '/api/execute_sql',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                sql: sql
            }),
            success: function (response) {
                if (response.success) {
                    // 显示查询结果
                    displayRankingData(response.data.results);
                    $('#paginationContainer').hide();
                    updateResultInfo(response.data.count, 1, response.data.count);
                } else {
                    alert('执行SQL失败: ' + response.error);
                }
            },
            error: function () {
                alert('执行SQL失败，请稍后重试');
            },
            complete: function () {
                $('#aiLoadingArea').hide();
                $('#executeSqlBtn').prop('disabled', false);
            }
        });
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function () {
            // 显示复制成功提示
            const btn = $('#copySqlBtn');
            const originalHtml = btn.html();
            btn.html('<i class="fas fa-check"></i>');
            setTimeout(() => {
                btn.html(originalHtml);
            }, 1000);
        }).catch(function () {
            alert('复制失败，请手动复制');
        });
    }

    // 列配置相关函数
    function applyColumnConfiguration() {
        const visibleColumns = [];
        $('.column-toggle:checked').each(function () {
            visibleColumns.push($(this).val());
        });

        // 存储列配置到本地存储
        localStorage.setItem('ranking_visible_columns', JSON.stringify(visibleColumns));

        // 更新表格显示
        updateTableColumnVisibility(visibleColumns);
    }

    function updateTableColumnVisibility(visibleColumns) {
        const table = $('#rankingTable');
        const headers = table.find('thead th');
        const rows = table.find('tbody tr');

        // 列配置映射 - 按照表格实际列顺序（从index.html中的表头定义）
        const columnMap = {
            'total_rank': 0,           // 排名
            'student_id': 1,           // 学号
            'name': 2,                 // 姓名
            'major_name': 3,           // 专业
            'class_name': 4,           // 班级
            'academic_year_semester': 5, // 学年学期
            'total_score': 6,          // 总分
            'academic_score': 7,       // 学业成绩
            'comprehensive_score': 8,  // 综合素质分
            'award_level': 9,          // 奖学金等级
            'actions': 10              // 操作
        };

        // 隐藏/显示列
        Object.keys(columnMap).forEach(column => {
            const columnIndex = columnMap[column];
            const isVisible = visibleColumns.includes(column);
            
            // 显示/隐藏表头
            headers.eq(columnIndex).toggle(isVisible);
            
            // 显示/隐藏数据行
            rows.each(function() {
                $(this).find('td').eq(columnIndex).toggle(isVisible);
            });
        });
        
        // 特殊处理：一些列始终显示
        const alwaysVisible = ['total_rank', 'student_id', 'name', 'actions'];
        alwaysVisible.forEach(column => {
            const columnIndex = columnMap[column];
            if (typeof columnIndex === 'number') {
                headers.eq(columnIndex).show();
                rows.each(function() {
                    $(this).find('td').eq(columnIndex).show();
                });
            }
        });
    }

    // 初始化列配置
    function initColumnConfiguration() {
        const savedColumns = localStorage.getItem('ranking_visible_columns');
        if (savedColumns) {
            const visibleColumns = JSON.parse(savedColumns);
            
            // 更新复选框状态
            $('.column-toggle').each(function() {
                const columnName = $(this).val();
                $(this).prop('checked', visibleColumns.includes(columnName));
            });
            
            // 应用列配置
            updateTableColumnVisibility(visibleColumns);
        }
    }

    // 多字段排序功能
    function addSortField() {
        const sortConfig = $('#sortConfig');
        const newSortItem = `
            <div class="row g-3 sort-item mt-2">
                <div class="col-md-4">
                    <select class="form-select sort-field">
                        <option value="total_score">总分</option>
                        <option value="academic_score">学业成绩</option>
                        <option value="comprehensive_score">综合素质分</option>
                        <option value="total_rank">总排名</option>
                        <option value="academic_rank">学业排名</option>
                        <option value="grade">年级</option>
                        <option value="moral_score">德育分</option>
                        <option value="social_work_score">社会工作分</option>
                        <option value="research_score">科研分</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select sort-direction">
                        <option value="desc">降序</option>
                        <option value="asc">升序</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeSortField(this)">
                        <i class="fas fa-minus"></i> 移除
                    </button>
                </div>
            </div>
        `;
        sortConfig.append(newSortItem);
    }

    function removeSortField(button) {
        $(button).closest('.sort-item').remove();
    }

    // 应用排序配置
    function applySortConfiguration() {
        const sortFields = [];
        $('.sort-item').each(function() {
            const field = $(this).find('.sort-field').val();
            const direction = $(this).find('.sort-direction').val();
            if (field) {
                sortFields.push({
                    field: field,
                    direction: direction
                });
            }
        });

        if (sortFields.length > 0) {
            // 支持多字段排序：将排序字段序列化为JSON字符串
            const sortFieldsJson = JSON.stringify(sortFields);
            currentFilters.sort_fields = sortFieldsJson;
            
            // 保持向后兼容：使用第一个排序字段作为主要排序
            currentFilters.order_by = sortFields[0].field;
            currentFilters.order_desc = sortFields[0].direction === 'desc' ? 'true' : 'false';
            
            // 重新加载数据
            currentPage = 1;
            loadRankingData(1);
            
            // 显示成功提示
            showSortAppliedMessage(sortFields.length);
        } else {
            alert('请至少配置一个排序字段');
        }
    }
    
    // 显示排序应用成功提示
    function showSortAppliedMessage(fieldCount) {
        const message = `排序配置已应用（${fieldCount}个字段）`;
        // 创建临时提示元素
        const alertDiv = $(`
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 1050;">
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        $('body').append(alertDiv);
        
        // 3秒后自动消失
        setTimeout(() => {
            alertDiv.alert('close');
        }, 3000);
    }

    function viewStudentAnalysis(studentId) {
        // 跳转到学生分析页面
        if (!studentId) {
            // 如果没有传入studentId，尝试从模态框获取
            studentId = $('#studentDetailModal').data('student-id');
        }
        
        if (studentId) {
            window.open(`/analysis/student/${studentId}`, '_blank');
        } else {
            alert('无法获取学生ID');
        }
    }

    // 清空AI数据
    function clearAIData() {
        $('#aiQuery').val('');
        $('#generatedSql').val('');
        $('#sqlResultArea').hide();
        $('#aiAnalysisArea').hide();
        $('#aiAnalysisContent').empty();
        $('#executeSqlBtn').prop('disabled', true);
    }
    
    // 重置排序配置
    function resetSortConfiguration() {
        // 清除所有额外的排序字段
        $('#sortConfig .sort-item').not(':first').remove();
        
        // 重置第一个排序字段为默认值
        $('#sortConfig .sort-item:first .sort-field').val('total_score');
        $('#sortConfig .sort-item:first .sort-direction').val('desc');
    }
    
    // 重置列显示配置
    function resetColumnConfiguration() {
        // 重置为默认显示的列
        const defaultColumns = [
            'student_id', 'name', 'major_name', 'class_name', 
            'academic_year_semester', 'total_score', 'academic_score', 
            'comprehensive_score', 'award_level'
        ];
        
        // 清除本地存储
        localStorage.removeItem('ranking_visible_columns');
        
        // 重置复选框状态
        $('.column-toggle').each(function() {
            const columnName = $(this).val();
            $(this).prop('checked', defaultColumns.includes(columnName));
        });
        
        // 应用默认配置
        updateTableColumnVisibility(defaultColumns);
    }
</script>
{% endblock %}