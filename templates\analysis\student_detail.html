{% extends "base.html" %}

{% block title %}学生详情分析 - 学生学业数据管理与分析平台{% endblock %}

{% block extra_head %}
<!-- Marked.js for Markdown rendering -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<!-- Highlight.js for code syntax highlighting -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>

<style>
.ai-analysis {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.ai-analysis-header {
    background: rgba(255,255,255,0.1);
    border-radius: 15px 15px 0 0;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.ai-analysis-header h6 {
    color: white;
    margin: 0;
    font-weight: 600;
}

.ai-analysis-content {
    background: white;
    border-radius: 0 0 15px 15px;
    padding: 25px;
    color: #333;
}

.ai-analysis-content h1,
.ai-analysis-content h2,
.ai-analysis-content h3,
.ai-analysis-content h4,
.ai-analysis-content h5,
.ai-analysis-content h6 {
    color: #2c3e50;
    margin-top: 25px;
    margin-bottom: 15px;
    font-weight: 600;
}

.ai-analysis-content h2 {
    font-size: 1.4rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.ai-analysis-content h3 {
    font-size: 1.2rem;
    color: #e74c3c;
}

.ai-analysis-content p {
    line-height: 1.8;
    margin-bottom: 15px;
    color: #444;
}

.ai-analysis-content ul,
.ai-analysis-content ol {
    margin: 15px 0;
    padding-left: 25px;
}

.ai-analysis-content li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.ai-analysis-content strong {
    color: #2c3e50;
    font-weight: 600;
}

.ai-analysis-content blockquote {
    border-left: 4px solid #3498db;
    margin: 20px 0;
    padding: 15px 20px;
    background: #f8f9fa;
    border-radius: 5px;
    font-style: italic;
}

.ai-analysis-content code {
    background: #f1f2f6;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', monospace;
    color: #e74c3c;
}

.ai-analysis-content pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    overflow-x: auto;
    margin: 15px 0;
}

.ai-analysis-content pre code {
    background: none;
    padding: 0;
    color: inherit;
}

.ai-analysis-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.ai-analysis-content table th,
.ai-analysis-content table td {
    border: 1px solid #ddd;
    padding: 12px;
    text-align: left;
}

.ai-analysis-content table th {
    background: #3498db;
    color: white;
    font-weight: 600;
}

.ai-analysis-content table tr:nth-child(even) {
    background: #f8f9fa;
}

.ai-analysis-content .emoji {
    font-size: 1.2em;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-user-graduate me-2"></i>
            学生详情分析
        </h1>
    </div>
</div>

{% if student_data %}
<!-- 学生基本信息 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-id-card me-2"></i>
                    基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <strong>学号：</strong>{{ student_data[0].student_id }}
                    </div>
                    <div class="col-md-2">
                        <strong>姓名：</strong>{{ student_data[0].name }}
                    </div>
                    <div class="col-md-3">
                        <strong>专业：</strong>{{ student_data[0].major_name }}
                    </div>
                    <div class="col-md-2">
                        <strong>班级：</strong>{{ student_data[0].class_name }}
                    </div>
                    <div class="col-md-3">
                        <strong>数据记录：</strong>{{ student_data|length }} 个学期
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 成绩趋势分析 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    成绩趋势分析
                </h5>
            </div>
            <div class="card-body">
                <div id="trendChart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 排名趋势分析 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    排名趋势
                </h5>
            </div>
            <div class="card-body">
                <div id="rankTrendChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    最新成绩构成
                </h5>
            </div>
            <div class="card-body">
                <div id="scoreCompositionChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- AI智能分析 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot me-2"></i>
                    AI智能分析
                </h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-primary" id="aiAnalysisBtn">
                    <i class="fas fa-magic me-1"></i>
                    生成AI分析报告
                </button>
                
                <div id="aiAnalysisResult" class="mt-3" style="display: none;">
                    <div class="ai-analysis">
                        <div class="ai-analysis-header">
                            <h6><i class="fas fa-brain me-2"></i>AI智能分析报告</h6>
                        </div>
                        <div class="ai-analysis-content" id="aiAnalysisContent">
                            <!-- AI分析内容将在这里显示 -->
                        </div>
                    </div>
                </div>
                
                <div id="aiAnalysisLoading" class="text-center mt-3" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">AI分析中...</span>
                    </div>
                    <p class="mt-2"><strong>AI正在深度分析学生数据...</strong></p>
                    <p class="text-muted">这可能需要1-3分钟，请耐心等待</p>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细成绩记录 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    详细成绩记录
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>学年学期</th>
                                <th>总分</th>
                                <th>总排名</th>
                                <th>学业成绩</th>
                                <th>学业排名</th>
                                <th>综合素质分</th>
                                <th>德育分</th>
                                <th>社会工作</th>
                                <th>科研分</th>
                                <th>活动分</th>
                                <th>集体分</th>
                                <th>奖学金等级</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in student_data %}
                            <tr>
                                <td>{{ record.academic_year }}-{{ record.semester }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ "%.2f"|format(record.total_score) }}</span>
                                </td>
                                <td>
                                    {% if record.total_rank %}
                                        {% if record.total_rank <= 3 %}
                                            <span class="badge bg-warning">{{ record.total_rank }}</span>
                                        {% elif record.total_rank <= 10 %}
                                            <span class="badge bg-info">{{ record.total_rank }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ record.total_rank }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>{{ "%.2f"|format(record.academic_score) }}</td>
                                <td>
                                    {% if record.academic_rank %}
                                        <span class="badge bg-success">{{ record.academic_rank }}</span>
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                                <td>{{ "%.2f"|format(record.comprehensive_score) }}</td>
                                <td>{{ "%.2f"|format(record.moral_score or 0) }}</td>
                                <td>{{ "%.2f"|format(record.social_work_score or 0) }}</td>
                                <td>{{ "%.2f"|format(record.research_score or 0) }}</td>
                                <td>{{ "%.2f"|format(record.activity_total_score or 0) }}</td>
                                <td>{{ "%.2f"|format(record.collective_score or 0) }}</td>
                                <td>
                                    {% if record.award_level %}
                                        {% if record.award_level == '一等奖学金' %}
                                            <span class="badge bg-warning">{{ record.award_level }}</span>
                                        {% elif record.award_level == '二等奖学金' %}
                                            <span class="badge bg-info">{{ record.award_level }}</span>
                                        {% elif record.award_level == '三等奖学金' %}
                                            <span class="badge bg-success">{{ record.award_level }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ record.award_level }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">无</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            未找到该学生的数据。
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
$(document).ready(function() {
    {% if student_data %}
    // 绘制图表
    drawTrendChart();
    drawRankTrendChart();
    drawScoreCompositionChart();
    
    // AI分析按钮事件
    $('#aiAnalysisBtn').on('click', function() {
        generateAIAnalysis();
    });
    {% endif %}
});

{% if student_data %}
function drawTrendChart() {
    const chart = echarts.init(document.getElementById('trendChart'));
    const data = {{ student_data | tojson }};
    
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        legend: {
            data: ['总分', '学业成绩', '综合素质分']
        },
        xAxis: {
            type: 'category',
            data: data.map(item => item.academic_year + '-' + item.semester)
        },
        yAxis: {
            type: 'value',
            name: '分数'
        },
        series: [
            {
                name: '总分',
                type: 'line',
                data: data.map(item => item.total_score.toFixed(2)),
                itemStyle: { color: '#4facfe' },
                smooth: true
            },
            {
                name: '学业成绩',
                type: 'line',
                data: data.map(item => item.academic_score.toFixed(2)),
                itemStyle: { color: '#28a745' },
                smooth: true
            },
            {
                name: '综合素质分',
                type: 'line',
                data: data.map(item => item.comprehensive_score.toFixed(2)),
                itemStyle: { color: '#ffc107' },
                smooth: true
            }
        ]
    };
    chart.setOption(option);
}

function drawRankTrendChart() {
    const chart = echarts.init(document.getElementById('rankTrendChart'));
    const data = {{ student_data | tojson }};
    
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        legend: {
            data: ['总排名', '学业排名']
        },
        xAxis: {
            type: 'category',
            data: data.map(item => item.academic_year + '-' + item.semester)
        },
        yAxis: {
            type: 'value',
            name: '排名',
            inverse: true, // 排名越小越好，所以反转Y轴
            min: function(value) {
                return Math.max(1, value.min - 5);
            }
        },
        series: [
            {
                name: '总排名',
                type: 'line',
                data: data.map(item => item.total_rank || null),
                itemStyle: { color: '#4facfe' },
                smooth: true,
                connectNulls: false
            },
            {
                name: '学业排名',
                type: 'line',
                data: data.map(item => item.academic_rank || null),
                itemStyle: { color: '#28a745' },
                smooth: true,
                connectNulls: false
            }
        ]
    };
    chart.setOption(option);
}

function drawScoreCompositionChart() {
    const chart = echarts.init(document.getElementById('scoreCompositionChart'));
    const latestData = {{ student_data[-1] | tojson }};
    
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
            name: '成绩构成',
            type: 'pie',
            radius: '70%',
            data: [
                { name: '德育分', value: latestData.moral_score || 0 },
                { name: '社会工作分', value: latestData.social_work_score || 0 },
                { name: '科研分', value: latestData.research_score || 0 },
                { name: '活动分', value: latestData.activity_total_score || 0 },
                { name: '集体分', value: latestData.collective_score || 0 }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };
    chart.setOption(option);
}

function generateAIAnalysis() {
    $('#aiAnalysisBtn').prop('disabled', true);
    $('#aiAnalysisLoading').show();
    $('#aiAnalysisResult').hide();
    
    $.ajax({
        url: '/analysis/api/student/{{ student_id }}/ai_analysis',
        method: 'POST',
        contentType: 'application/json',
        timeout: 300000, // 5分钟超时
        success: function(response) {
            if (response.success) {
                try {
                    // 使用marked.js渲染markdown
                    const markdownContent = response.data.analysis;
                    
                    // 检查marked是否可用
                    if (typeof marked === 'undefined') {
                        throw new Error('marked.js library not loaded');
                    }
                    
                    // 渲染markdown
                    const htmlContent = marked.parse(markdownContent);
                    $('#aiAnalysisContent').html(htmlContent);
                    
                    // 代码高亮
                    if (typeof hljs !== 'undefined') {
                        hljs.highlightAll();
                    }
                    
                    $('#aiAnalysisResult').show();
                    
                    // 添加成功提示
                    showSuccessToast('AI分析报告生成成功！');
                } catch (error) {
                    console.error('Markdown rendering error:', error);
                    // 如果markdown渲染失败，直接显示原始内容
                    $('#aiAnalysisContent').html('<pre>' + response.data.analysis + '</pre>');
                    $('#aiAnalysisResult').show();
                    showSuccessToast('AI分析报告生成成功！（原始格式）');
                }
            } else {
                showErrorAlert('AI分析失败: ' + response.error);
            }
        },
        error: function(xhr, status, error) {
            if (status === 'timeout') {
                showErrorAlert('AI分析超时，请稍后重试');
            } else {
                showErrorAlert('AI分析失败，请稍后重试');
            }
        },
        complete: function() {
            $('#aiAnalysisBtn').prop('disabled', false);
            $('#aiAnalysisLoading').hide();
        }
    });
}

// 显示成功提示
function showSuccessToast(message) {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-success border-0" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    $('body').append(toastHtml);
    const toast = new bootstrap.Toast($('.toast').last()[0]);
    toast.show();
}

// 显示错误提示
function showErrorAlert(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('body').append(alertHtml);
    
    // 5秒后自动消失
    setTimeout(() => {
        $('.alert').last().alert('close');
    }, 5000);
}
{% endif %}
</script>
{% endblock %}
