#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库操作模块
扩展现有数据库，添加用户管理、日志等功能
"""

import sqlite3
import logging
from datetime import datetime
from contextlib import contextmanager

logger = logging.getLogger(__name__)

def get_db_connection(db_path='scholarship_data.db'):
    """获取数据库连接"""
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row  # 使查询结果可以像字典一样访问
    return conn

@contextmanager
def get_db_cursor(db_path='scholarship_data.db'):
    """数据库游标上下文管理器"""
    conn = get_db_connection(db_path)
    try:
        cursor = conn.cursor()
        yield cursor
        conn.commit()
    except Exception as e:
        conn.rollback()
        raise e
    finally:
        conn.close()

def init_db(db_path='scholarship_data.db'):
    """初始化数据库，创建新增的表"""
    conn = get_db_connection(db_path)
    cursor = conn.cursor()
    
    try:
        # 创建用户表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password_hash TEXT NOT NULL,
                email TEXT,
                full_name TEXT,
                role TEXT NOT NULL DEFAULT 'student',  -- admin, teacher, student
                student_id TEXT,  -- 关联学生学号（仅学生用户）
                is_active BOOLEAN DEFAULT 1,
                is_sub_admin BOOLEAN DEFAULT 0,  -- 教师是否为子管理员
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        # 创建系统日志表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                description TEXT,
                ip_address TEXT,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # 创建API配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_key TEXT NOT NULL UNIQUE,
                config_value TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建用户反馈表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS feedback (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                status TEXT DEFAULT 'pending',  -- pending, replied, closed
                admin_reply TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # 创建数据导入记录表（扩展现有的file_records）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS import_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                file_name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                operation_type TEXT NOT NULL,  -- import, export, delete
                record_count INTEGER DEFAULT 0,
                status TEXT DEFAULT 'success',  -- success, failed, partial
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # 插入默认API配置
        cursor.execute('''
            INSERT OR IGNORE INTO api_configs (config_key, config_value, description) VALUES
            ('deepseek_api_key', '***********************************', 'DeepSeek API密钥'),
            ('deepseek_api_url', 'https://dashscope.aliyuncs.com/compatible-mode/v1', '阿里云百炼API端点'),
            ('deepseek_model', 'deepseek-r1', 'DeepSeek模型名称'),
            ('ai_temperature', '0.7', 'AI温度系数'),
            ('ai_max_tokens', '2000', 'AI最大输出token数'),
            ('analysis_prompt', '请分析该学生的学业表现，包括成绩趋势、排名变化、优势科目等方面。', '学生分析Prompt'),
            ('sql_prompt', '你是一个SQL专家，请根据用户的自然语言描述生成对应的SQL查询语句。', 'SQL生成Prompt')
        ''')
        
        # 创建默认管理员用户（如果不存在）
        from werkzeug.security import generate_password_hash
        admin_password_hash = generate_password_hash('admin123')
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password_hash, full_name, role, is_active) 
            VALUES ('admin', ?, '系统管理员', 'admin', 1)
        ''', (admin_password_hash,))
        
        conn.commit()
        logger.info("数据库初始化完成")
        
    except Exception as e:
        conn.rollback()
        logger.error(f"数据库初始化失败: {e}")
        raise e
    finally:
        conn.close()

def log_user_action(user_id, action, description=None, ip_address=None, user_agent=None, db_path='scholarship_data.db'):
    """记录用户操作日志"""
    try:
        with get_db_cursor(db_path) as cursor:
            cursor.execute('''
                INSERT INTO system_logs (user_id, action, description, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, action, description, ip_address, user_agent))
    except Exception as e:
        logger.error(f"记录用户操作日志失败: {e}")

def get_api_config(key, default=None, db_path='scholarship_data.db'):
    """获取API配置"""
    try:
        with get_db_cursor(db_path) as cursor:
            cursor.execute('SELECT config_value FROM api_configs WHERE config_key = ?', (key,))
            result = cursor.fetchone()
            return result['config_value'] if result else default
    except Exception as e:
        logger.error(f"获取API配置失败: {e}")
        return default

def set_api_config(key, value, description=None, db_path='scholarship_data.db'):
    """设置API配置"""
    try:
        with get_db_cursor(db_path) as cursor:
            cursor.execute('''
                INSERT OR REPLACE INTO api_configs (config_key, config_value, description, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (key, value, description))
    except Exception as e:
        logger.error(f"设置API配置失败: {e}")
        raise e
